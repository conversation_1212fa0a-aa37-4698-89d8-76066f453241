import { cleanupSession, getTabSessionId } from '../sessionUtils';
import TranscriptionsQueue from '../transcriptionsQueue';

// Mock logger to avoid console output during tests
jest.mock('@carepatron/utilities', () => ({
	logger: {
		info: jest.fn(),
		error: jest.fn(),
	},
}));

// Mock sessionStorage
const mockSessionStorage = {
	getItem: jest.fn(),
	setItem: jest.fn(),
	removeItem: jest.fn(),
};

Object.defineProperty(window, 'sessionStorage', {
	value: mockSessionStorage,
});

// Mock BroadcastChannel
class MockBroadcastChannel {
	name: string;

	constructor(name: string) {
		this.name = name;
	}

	addEventListener() {
		// Mock implementation
	}

	removeEventListener() {
		// Mock implementation
	}

	postMessage() {
		// Mock implementation
	}

	close() {
		// Mock implementation
	}
}

Object.defineProperty(window, 'BroadcastChannel', {
	value: MockBroadcastChannel,
});

describe('Memory Leak Fixes', () => {
	beforeEach(() => {
		jest.clearAllMocks();
		mockSessionStorage.getItem.mockReturnValue(null);

		// Clear any existing instances
		// @ts-ignore - accessing private static property for testing
		TranscriptionsQueue.instances?.clear();
	});

	describe('TranscriptionsQueue cleanup', () => {
		it('should clean up queue instances when cleanup is called', () => {
			// Create multiple instances
			mockSessionStorage.getItem
				.mockReturnValueOnce('session-1')
				.mockReturnValueOnce('session-2')
				.mockReturnValueOnce('session-3');

			const queue1 = TranscriptionsQueue.getInstance();
			const queue2 = TranscriptionsQueue.getInstance();
			const queue3 = TranscriptionsQueue.getInstance();

			// Verify instances were created
			expect(TranscriptionsQueue.getInstanceCount()).toBe(3);

			// Clean up one instance
			TranscriptionsQueue.cleanup('session-1');

			// Verify instance count decreased
			expect(TranscriptionsQueue.getInstanceCount()).toBe(2);

			// Clean up remaining instances
			TranscriptionsQueue.cleanup('session-2');
			TranscriptionsQueue.cleanup('session-3');

			// Verify all instances cleaned up
			expect(TranscriptionsQueue.getInstanceCount()).toBe(0);
		});

		it('should handle cleanup of non-existent session gracefully', () => {
			expect(() => {
				TranscriptionsQueue.cleanup('non-existent-session');
			}).not.toThrow();

			expect(TranscriptionsQueue.getInstanceCount()).toBe(0);
		});
	});

	describe('Session cleanup', () => {
		it('should remove session from sessionStorage when cleaning up current session', () => {
			const sessionId = 'test-session-123';
			mockSessionStorage.getItem.mockReturnValue(sessionId);

			cleanupSession(sessionId);

			expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('transcription_tab_session_id');
		});

		it('should not remove session from sessionStorage when cleaning up different session', () => {
			const currentSessionId = 'current-session';
			const otherSessionId = 'other-session';
			mockSessionStorage.getItem.mockReturnValue(currentSessionId);

			cleanupSession(otherSessionId);

			expect(mockSessionStorage.removeItem).not.toHaveBeenCalled();
		});
	});

	describe('Integration test', () => {
		it('should properly clean up all resources', () => {
			// Simulate multiple tabs
			const sessions = ['session-1', 'session-2', 'session-3'];

			sessions.forEach((sessionId, index) => {
				mockSessionStorage.getItem.mockReturnValueOnce(sessionId);
				TranscriptionsQueue.getInstance();
			});

			expect(TranscriptionsQueue.getInstanceCount()).toBe(3);

			// Simulate tabs closing
			sessions.forEach((sessionId) => {
				TranscriptionsQueue.cleanup(sessionId);
				cleanupSession(sessionId);
			});

			expect(TranscriptionsQueue.getInstanceCount()).toBe(0);
		});

		it('should preserve expected orphan adoption behavior after cleanup', () => {
			// Scenario 1: Recording tab closes, other tabs should be able to adopt
			mockSessionStorage.getItem.mockReturnValueOnce('recording-session').mockReturnValueOnce('other-session');

			const recordingQueue = TranscriptionsQueue.getInstance();
			const otherQueue = TranscriptionsQueue.getInstance();

			expect(TranscriptionsQueue.getInstanceCount()).toBe(2);

			// Simulate recording tab closing (cleanup only removes instance, not data)
			TranscriptionsQueue.cleanup('recording-session');
			cleanupSession('recording-session');

			expect(TranscriptionsQueue.getInstanceCount()).toBe(1);

			// Other tab should still be able to create new instances and access IndexedDB
			// (In real scenario, IndexedDB data would persist and be accessible)
			mockSessionStorage.getItem.mockReturnValueOnce('new-session');
			const newQueue = TranscriptionsQueue.getInstance();

			expect(TranscriptionsQueue.getInstanceCount()).toBe(2);
		});

		it('should allow new tabs to resume processing after all tabs close', () => {
			// Scenario 2: All tabs close, then new tab opens
			const sessions = ['session-1', 'session-2'];

			sessions.forEach((sessionId) => {
				mockSessionStorage.getItem.mockReturnValueOnce(sessionId);
				TranscriptionsQueue.getInstance();
			});

			expect(TranscriptionsQueue.getInstanceCount()).toBe(2);

			// All tabs close
			sessions.forEach((sessionId) => {
				TranscriptionsQueue.cleanup(sessionId);
				cleanupSession(sessionId);
			});

			expect(TranscriptionsQueue.getInstanceCount()).toBe(0);

			// New tab opens - should be able to create instance and access IndexedDB
			mockSessionStorage.getItem.mockReturnValueOnce('new-session-after-restart');
			const newQueue = TranscriptionsQueue.getInstance();

			expect(TranscriptionsQueue.getInstanceCount()).toBe(1);
			expect(newQueue.getSessionId()).toBe('new-session-after-restart');
		});
	});
});
